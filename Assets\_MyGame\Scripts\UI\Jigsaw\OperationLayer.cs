using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

/// <summary>
/// 操作层组件，包含网格显示功能
/// </summary>
public class OperationLayer
{
    private readonly GComponent operationComponent;

    // 网格配置
    public int gridColumns = 6;
    public int gridRows = 8;
    public Color gridLineColor = Color.white;
    public float gridLineWidth = 1f;
    public bool showGrid = true;

    private GComponent layerThickness;
    private GComponent layerGrid;
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="component">操作层的GComponent对象</param>
    public OperationLayer(GComponent component)
    {
        operationComponent = component;
        layerThickness = operationComponent.GetChild("layerThickness").asCom;
        layerGrid = new GComponent();
        layerGrid.touchable = false;
        operationComponent.AddChildAt(layerGrid, 1);
        DoInitialize();
    }

    private void DoInitialize()
    {
        // 设置网格线颜色为半透明白色
        gridLineColor = new Color(1f, 1f, 1f, 0.3f);

        // 绘制网格
        if (showGrid)
        {
            DrawGrid();
        }
    }
    
    /// <summary>
    /// 绘制网格线
    /// </summary>
    private void DrawGrid()
    {
        if (operationComponent == null) return;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        // 绘制网格线
        DrawGridLines(cellWidth, cellHeight);
    }
    
    /// <summary>
    /// 绘制网格线（使用矩形边框方式）
    /// </summary>
    private void DrawGridLines(float cellWidth, float cellHeight)
    {
        // 绘制垂直线
        for (int i = 1; i < gridColumns; i++)
        {
            float x = i * cellWidth;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_v_{i}";
            lineGraph.SetSize(gridLineWidth, operationComponent.height);
            lineGraph.SetXY(x - gridLineWidth / 2, 0);
            lineGraph.DrawRect(gridLineWidth, operationComponent.height, 0, Color.clear, gridLineColor);
            
            layerGrid.AddChild(lineGraph);
        }

        // 绘制水平线
        for (int i = 1; i < gridRows; i++)
        {
            float y = i * cellHeight;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_h_{i}";
            lineGraph.SetSize(operationComponent.width, gridLineWidth);
            lineGraph.SetXY(0, y - gridLineWidth / 2);
            lineGraph.DrawRect(operationComponent.width, gridLineWidth, 0, Color.clear, gridLineColor);
            layerGrid.AddChild(lineGraph);
        }
    }
    
    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        showGrid = visible;
        
        if (showGrid)
        {
            DrawGrid();
        }
        else
        {
            ClearGrid();
        }
    }
    
    /// <summary>
    /// 清除网格显示
    /// </summary>
    private void ClearGrid()
    {
        if (operationComponent == null) return;

        // 移除所有网格线
        for (int i = operationComponent.numChildren - 1; i >= 0; i--)
        {
            var child = operationComponent.GetChildAt(i);
            if (child.name != null && child.name.StartsWith("gridLine_"))
            {
                operationComponent.RemoveChildAt(i, true);
            }
        }
    }
    
    /// <summary>
    /// 更新网格配置
    /// </summary>
    /// <param name="columns">列数</param>
    /// <param name="rows">行数</param>
    /// <param name="lineColor">线条颜色</param>
    /// <param name="lineWidth">线条宽度</param>
    public void UpdateGridConfig(int columns, int rows, Color lineColor, float lineWidth)
    {
        gridColumns = columns;
        gridRows = rows;
        gridLineColor = lineColor;
        gridLineWidth = lineWidth;
        
        if (showGrid)
        {
            ClearGrid();
            DrawGrid();
        }
    }
    
    /// <summary>
    /// 获取指定位置对应的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        if (operationComponent == null) return Vector2Int.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        int gridX = Mathf.FloorToInt(localPosition.x / cellWidth);
        int gridY = Mathf.FloorToInt(localPosition.y / cellHeight);

        // 限制在网格范围内
        gridX = Mathf.Clamp(gridX, 0, gridColumns - 1);
        gridY = Mathf.Clamp(gridY, 0, gridRows - 1);

        return new Vector2Int(gridX, gridY);
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置（网格中心点）
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        if (operationComponent == null) return Vector2.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        float x = (gridPosition.x + 0.5f) * cellWidth;
        float y = (gridPosition.y + 0.5f) * cellHeight;

        return new Vector2(x, y);
    }

    // 用于存储拼块与其在layerThickness中对应thickness的映射关系
    private Dictionary<JigsawPiece, GComponent> pieceThicknessMap =
        new Dictionary<JigsawPiece, GComponent>();

    /// <summary>
    /// 将拼块的thickness添加到layerThickness中
    /// </summary>
    /// <param name="piece">拼块对象</param>
    public void AddPieceThicknessToLayer(JigsawPiece piece)
    {
        if (layerThickness == null || piece == null) return;

        // 如果已经存在映射，先移除
        if (pieceThicknessMap.ContainsKey(piece))
        {
            RemovePieceThicknessFromLayer(piece);
        }

        // 获取拼块的thickness
        var pieceThickness = piece.GetThickness();
        if (pieceThickness == null) return;

        var thicknessClone = UIPackage.CreateObject("Jigsaw", "JigsawThickness").asCom;
        var thickness = thicknessClone.GetChild("thickness").asLoader;

        var url = $"ui://Z_Image_{piece.imageIndex}/piece_{piece.pieceIndex}";
        thickness.url = url;


        // 计算拼块在操作层中的位置
        Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
        Vector2 layerLocalPos = layerThickness.GlobalToLocal(pieceGlobalPos);
        thicknessClone.SetXY(layerLocalPos.x, layerLocalPos.y);

        // 添加到layerThickness中
        layerThickness.AddChild(thicknessClone);

        // 建立映射关系
        pieceThicknessMap[piece] = thicknessClone;

        // 隐藏拼块自身的thickness
        piece.SetThicknessVisible(false);
    }

    /// <summary>
    /// 从layerThickness中移除拼块的thickness
    /// </summary>
    /// <param name="piece">拼块对象</param>
    public void RemovePieceThicknessFromLayer(JigsawPiece piece)
    {
        if (layerThickness == null || piece == null) return;

        // 通过映射关系查找对应的thickness
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thicknessClone))
        {
            // 从layerThickness中移除
            layerThickness.RemoveChild(thicknessClone, true);

            // 移除映射关系
            pieceThicknessMap.Remove(piece);
        }

        // 显示拼块自身的thickness
        piece.SetThicknessVisible(true);
    }

    /// <summary>
    /// 获取layerThickness组件
    /// </summary>
    /// <returns>layerThickness组件</returns>
    public GComponent GetLayerThickness()
    {
        return layerThickness;
    }

    /// <summary>
    /// 清理所有拼块的thickness映射
    /// </summary>
    public void ClearAllPieceThickness()
    {
        if (layerThickness == null) return;

        // 移除所有thickness子对象
        foreach (var thicknessClone in pieceThicknessMap.Values)
        {
            if (thicknessClone != null)
            {
                layerThickness.RemoveChild(thicknessClone, true);
            }
        }

        // 清空映射
        pieceThicknessMap.Clear();
    }

    /// <summary>
    /// 更新拼块thickness在layerThickness中的位置
    /// </summary>
    /// <param name="piece">拼块对象</param>
    public void UpdatePieceThicknessPosition(JigsawPiece piece)
    {
        if (layerThickness == null || piece == null) return;

        // 通过映射关系查找对应的thickness
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thicknessClone))
        {
            // 重新计算拼块在操作层中的位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
            Vector2 layerLocalPos = layerThickness.GlobalToLocal(pieceGlobalPos);
            thicknessClone.SetXY(layerLocalPos.x, layerLocalPos.y);
        }
    }
}
