using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

namespace UI.Jigsaw
{
    /// <summary>
    /// 拼块组管理类，用于管理相邻拼块的成组功能
    /// </summary>
    public class JigsawGroup
    {
        private List<JigsawPiece> pieces = new List<JigsawPiece>();
        private JigsawPanel parentPanel;
        private bool isDragging = false;
        private Vector2 dragStartOffset;
        
        // 拼图网格配置
        private const int GRID_COLUMNS = 6;
        private const int GRID_ROWS = 8;
        
        public JigsawGroup(JigsawPanel panel)
        {
            parentPanel = panel;
        }
        
        /// <summary>
        /// 获取组中的拼块数量
        /// </summary>
        public int Count => pieces.Count;
        
        /// <summary>
        /// 获取组中的所有拼块
        /// </summary>
        public List<JigsawPiece> Pieces => new List<JigsawPiece>(pieces);
        
        /// <summary>
        /// 添加拼块到组中
        /// </summary>
        /// <param name="piece">要添加的拼块</param>
        /// <returns>是否成功添加</returns>
        public bool AddPiece(JigsawPiece piece)
        {
            if (piece == null || pieces.Contains(piece))
                return false;
                
            // 检查是否与组中现有拼块相邻
            if (pieces.Count > 0 && !IsAdjacentToGroup(piece))
                return false;
                
            pieces.Add(piece);
            piece.SetGroup(this);
            
            // 更新视觉效果
            UpdateGroupVisual();
            
            return true;
        }
        
        /// <summary>
        /// 从组中移除拼块
        /// </summary>
        /// <param name="piece">要移除的拼块</param>
        /// <returns>是否成功移除</returns>
        public bool RemovePiece(JigsawPiece piece)
        {
            if (piece == null || !pieces.Contains(piece))
                return false;
                
            pieces.Remove(piece);
            piece.SetGroup(null);
            
            // 更新视觉效果
            UpdateGroupVisual();
            
            return true;
        }
        
        /// <summary>
        /// 检查拼块是否与组中的拼块相邻
        /// </summary>
        /// <param name="piece">要检查的拼块</param>
        /// <returns>是否相邻</returns>
        private bool IsAdjacentToGroup(JigsawPiece piece)
        {
            Vector2Int pieceGridPos = GetGridPosition(piece.pieceIndex);
            
            foreach (var groupPiece in pieces)
            {
                Vector2Int groupPieceGridPos = GetGridPosition(groupPiece.pieceIndex);
                if (IsAdjacent(pieceGridPos, groupPieceGridPos))
                    return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 根据拼块索引计算在6x8网格中的位置
        /// </summary>
        /// <param name="pieceIndex">拼块索引</param>
        /// <returns>网格位置</returns>
        public static Vector2Int GetGridPosition(int pieceIndex)
        {
            int x = pieceIndex % GRID_COLUMNS;
            int y = pieceIndex / GRID_COLUMNS;
            return new Vector2Int(x, y);
        }
        
        /// <summary>
        /// 检查两个网格位置是否相邻（水平或垂直相邻）
        /// </summary>
        /// <param name="pos1">位置1</param>
        /// <param name="pos2">位置2</param>
        /// <returns>是否相邻</returns>
        public static bool IsAdjacent(Vector2Int pos1, Vector2Int pos2)
        {
            int deltaX = Mathf.Abs(pos1.x - pos2.x);
            int deltaY = Mathf.Abs(pos1.y - pos2.y);
            
            // 相邻的条件：一个方向差值为1，另一个方向差值为0
            return (deltaX == 1 && deltaY == 0) || (deltaX == 0 && deltaY == 1);
        }
        
        /// <summary>
        /// 开始组拖拽
        /// </summary>
        /// <param name="draggedPiece">被拖拽的拼块</param>
        /// <param name="startPosition">拖拽开始位置</param>
        public void StartGroupDrag(JigsawPiece draggedPiece, Vector2 startPosition)
        {
            if (!pieces.Contains(draggedPiece))
                return;
                
            isDragging = true;
            dragStartOffset = startPosition - draggedPiece.xy;
            
            // 记录所有拼块的初始位置
            foreach (var piece in pieces)
            {
                if (piece != draggedPiece)
                {
                    piece.SetGroupDragOffset(piece.xy - draggedPiece.xy);
                }
            }
        }
        
        /// <summary>
        /// 更新组拖拽
        /// </summary>
        /// <param name="draggedPiece">被拖拽的拼块</param>
        /// <param name="currentPosition">当前位置</param>
        public void UpdateGroupDrag(JigsawPiece draggedPiece, Vector2 currentPosition)
        {
            if (!isDragging || !pieces.Contains(draggedPiece))
                return;
                
            Vector2 targetPosition = currentPosition - dragStartOffset;
            
            // 更新所有其他拼块的位置
            foreach (var piece in pieces)
            {
                if (piece != draggedPiece)
                {
                    Vector2 pieceTargetPos = targetPosition + piece.GetGroupDragOffset();
                    piece.SetXY(pieceTargetPos.x, pieceTargetPos.y);
                }
            }
        }
        
        /// <summary>
        /// 结束组拖拽
        /// </summary>
        public void EndGroupDrag()
        {
            isDragging = false;
            
            // 清除所有拼块的拖拽偏移
            foreach (var piece in pieces)
            {
                piece.ClearGroupDragOffset();
            }
        }
        
        /// <summary>
        /// 更新组的视觉效果
        /// </summary>
        private void UpdateGroupVisual()
        {
            // 为组中的拼块添加视觉标识（例如边框高亮）
            foreach (var piece in pieces)
            {
                piece.SetGroupHighlight(true);
            }
        }
        
        /// <summary>
        /// 清除组的视觉效果
        /// </summary>
        public void ClearGroupVisual()
        {
            foreach (var piece in pieces)
            {
                piece.SetGroupHighlight(false);
            }
        }
        
        /// <summary>
        /// 销毁组
        /// </summary>
        public void Dispose()
        {
            ClearGroupVisual();
            
            foreach (var piece in pieces)
            {
                piece.SetGroup(null);
            }
            
            pieces.Clear();
        }
        
        /// <summary>
        /// 检查是否可以与另一个组合并
        /// </summary>
        /// <param name="otherGroup">另一个组</param>
        /// <returns>是否可以合并</returns>
        public bool CanMergeWith(JigsawGroup otherGroup)
        {
            if (otherGroup == null || otherGroup == this)
                return false;
                
            // 检查两个组之间是否有相邻的拼块
            foreach (var piece1 in pieces)
            {
                foreach (var piece2 in otherGroup.pieces)
                {
                    Vector2Int pos1 = GetGridPosition(piece1.pieceIndex);
                    Vector2Int pos2 = GetGridPosition(piece2.pieceIndex);
                    if (IsAdjacent(pos1, pos2))
                        return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 与另一个组合并
        /// </summary>
        /// <param name="otherGroup">要合并的组</param>
        /// <returns>是否成功合并</returns>
        public bool MergeWith(JigsawGroup otherGroup)
        {
            if (!CanMergeWith(otherGroup))
                return false;
                
            // 将另一个组的所有拼块添加到当前组
            var otherPieces = new List<JigsawPiece>(otherGroup.pieces);
            foreach (var piece in otherPieces)
            {
                otherGroup.RemovePiece(piece);
                AddPiece(piece);
            }
            
            return true;
        }
    }
}
