using System;
using FairyGUI;
using UnityEngine;

public class JigsawPanel : Panel
{
    public JigsawPanel()
    {
        packName = "Jigsaw";
        compName = "JigsawPanel";
    }

    private OperationLayer oprationLayer;
    private GList listStorage;
    private GComponent oprationComponent;
    private GComponent dragLayer;
    private int imageIndex;
    protected override void DoInitialize()
    {
        UIObjectFactory.SetPackageItemExtension($"ui://{packName}/JigsawPiece", () => { return new JigsawPiece(); });

        oprationComponent = contentPane.GetChild("oprationLayer").asCom;
        oprationLayer = new OperationLayer(oprationComponent);
        listStorage = contentPane.GetChild("listStorage").asList;

        // 创建并添加拖拽层
        dragLayer = new GComponent();
        dragLayer.name = "DragLayer";
        // contentPane.fairyBatching = false;
        contentPane.AddChild(dragLayer);
        // 确保拖拽层在最上层
        contentPane.SetChildIndex(dragLayer, contentPane.numChildren - 1);

        listStorage.itemRenderer = UpdateStoragePiece;

        SetData();
    }

    public void SetData()
    {
        imageIndex = 1;
        FUILoader.LoadPackage($"Z_Image_{imageIndex}",()=>
        {
            if(contentPane == null || contentPane.isDisposed) return;
            listStorage.numItems = 6 * 8;
        });
    }

    private void UpdateStoragePiece(int index, GObject item)
    {
        var piece = item as JigsawPiece;
        piece.SetPiece(imageIndex, index);

        // 设置父面板引用，用于拖拽处理
        piece.SetParentPanel(this);
    }

    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        oprationLayer?.SetGridVisible(visible);
    }

    /// <summary>
    /// 更新网格配置
    /// </summary>
    /// <param name="columns">列数</param>
    /// <param name="rows">行数</param>
    /// <param name="lineColor">线条颜色</param>
    /// <param name="lineWidth">线条宽度</param>
    public void UpdateGridConfig(int columns, int rows, Color lineColor, float lineWidth)
    {
        oprationLayer?.UpdateGridConfig(columns, rows, lineColor, lineWidth);
    }

    /// <summary>
    /// 获取操作层的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        return oprationLayer?.GetGridPosition(localPosition) ?? Vector2Int.zero;
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        return oprationLayer?.GetLocalPosition(gridPosition) ?? Vector2.zero;
    }

    /// <summary>
    /// 将全局坐标转换为操作层的本地坐标
    /// </summary>
    /// <param name="globalPosition">全局坐标</param>
    /// <returns>操作层本地坐标</returns>
    public Vector2 GlobalToOperationLayerLocal(Vector2 globalPosition)
    {
        return oprationComponent.GlobalToLocal(globalPosition);
    }

    /// <summary>
    /// 将操作层本地坐标转换为全局坐标
    /// </summary>
    /// <param name="localPosition">操作层本地坐标</param>
    /// <returns>全局坐标</returns>
    public Vector2 OperationLayerLocalToGlobal(Vector2 localPosition)
    {
        return oprationComponent.LocalToGlobal(localPosition);
    }

    /// <summary>
    /// 检查位置是否在操作层范围内
    /// </summary>
    /// <param name="operationLayerLocalPos">操作层本地坐标</param>
    /// <returns>是否在范围内</returns>
    public bool IsPositionInOperationLayer(Vector2 operationLayerLocalPos)
    {
        return operationLayerLocalPos.x >= 0 && operationLayerLocalPos.y >= 0 &&
               operationLayerLocalPos.x <= oprationComponent.width &&
               operationLayerLocalPos.y <= oprationComponent.height;
    }

    /// <summary>
    /// 获取内容面板，用于拖拽时的父容器切换
    /// </summary>
    /// <returns>内容面板</returns>
    public GComponent GetContentPane()
    {
        return contentPane;
    }

    /// <summary>
    /// 获取操作层对象
    /// </summary>
    /// <returns>操作层对象</returns>
    public OperationLayer GetOperationLayer()
    {
        return oprationLayer;
    }

    /// <summary>
    /// 获取拖拽层容器，确保拖拽的拼块在最顶层
    /// </summary>
    /// <returns>拖拽层容器</returns>
    public GComponent GetDragLayer()
    {
        // 返回专用的拖拽层
        return dragLayer;
    }
}
