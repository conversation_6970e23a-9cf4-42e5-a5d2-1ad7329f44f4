using System;
using FairyGUI;
using FairyGUI.Utils;
using UnityEngine;

public class JigsawPiece : GComponent
{
    public override void ConstructFromXML(XML xml)
    {
        base.ConstructFromXML(xml);
        DoInitialize();
    }

    private GLoader thickness;
    private GLoader picture;

    public int imageIndex;
    public int pieceIndex;
    private JigsawPanel parentPanel;
    private Vector2 originalPosition;
    private Vector2 globalOriginalPosition;
    private bool isDragging = false;
    private GComponent originalParent;  // 保存原始父容器
    private int originalChildIndex;     // 保存在原始父容器中的索引位置

    // 自定义拖拽控制变量
    private bool customDragEnabled = false;
    private Vector2 touchStartPos;
    private bool isDragTesting = false;
    private const float VERTICAL_DRAG_THRESHOLD = 50f; // 垂直拖拽阈值

    // 克隆相关变量
    private JigsawPiece originalPiece;  // 如果这是克隆的piece，保存原始piece的引用
    private bool isClone = false;       // 标记是否为克隆的piece

    // 成组相关变量
    private JigsawGroup currentGroup;   // 当前所属的组
    private Vector2 groupDragOffset;    // 组拖拽时的偏移量
    private bool isGroupHighlighted = false; // 是否显示组高亮效果

    private void DoInitialize()
    {
        var thicknessComp = GetChild("thicknessComp").asCom;
        thickness = thicknessComp.GetChild("thickness").asLoader;
        // shadow.visible = false;

        picture = GetChild("picture").asLoader;

        // picture.visible = false;

        // 禁用默认拖拽功能，使用自定义拖拽控制
        draggable = false;

        // 绑定触摸事件来实现自定义拖拽控制
        onTouchBegin.Add(OnTouchBegin);
        onTouchMove.Add(OnTouchMove);
        onTouchEnd.Add(OnTouchEnd);

        // 绑定拖拽事件
        onDragStart.Add(OnDragStart);
        onDragMove.Add(OnDragMove);
        onDragEnd.Add(OnDragEnd);
    }
    internal void SetPiece(int imageIndex, int index)
    {
        this.imageIndex = imageIndex;
        this.pieceIndex = index;

        var url = $"ui://Z_Image_{imageIndex}/piece_{index}";
        thickness.url = picture.url = url;
    }

    /// <summary>
    /// 克隆当前拼块，创建一个新的JigsawPiece实例
    /// </summary>
    /// <returns>克隆的JigsawPiece</returns>
    public JigsawPiece Clone()
    {
        // 创建新的JigsawPiece实例
        JigsawPiece clonedPiece = UIPackage.CreateObject("Jigsaw", "JigsawPiece") as JigsawPiece;
        clonedPiece.SetPiece(imageIndex, pieceIndex);

        // clonedPiece.fairyBatching = true;
        // // 复制基本属性
        clonedPiece.pieceIndex = this.pieceIndex;
        clonedPiece.parentPanel = this.parentPanel;
        clonedPiece.isClone = true;
        clonedPiece.originalPiece = this.isClone ? this.originalPiece : this;  // 如果当前已经是克隆，则指向原始piece


        // 设置尺寸和位置
        clonedPiece.SetSize(this.width, this.height);
        clonedPiece.SetXY(this.x, this.y);

        return clonedPiece;
    }

    /// <summary>
    /// 获取原始piece（如果当前是克隆piece）
    /// </summary>
    /// <returns>原始piece或自身</returns>
    public JigsawPiece GetOriginalPiece()
    {
        return isClone ? originalPiece : this;
    }

    /// <summary>
    /// 检查是否为克隆piece
    /// </summary>
    /// <returns>是否为克隆piece</returns>
    public bool IsClone()
    {
        return isClone;
    }

    /// <summary>
    /// 创建克隆piece并开始拖拽
    /// </summary>
    /// <param name="evt">输入事件</param>
    private void CreateCloneAndStartDrag(InputEvent evt)
    {
        if (parentPanel == null) return;

        JigsawPiece dragPiece = this.Clone();
        
        // 设置当前piece（原始piece）透明度
        this.alpha = 0.3f;

        // 将克隆的piece添加到JagsawPanel的拖拽层中
        var dragLayer = parentPanel.GetDragLayer();
        Vector2 cloneGlobalPos = this.LocalToGlobal(Vector2.zero);
        Vector2 cloneLocalPos = dragLayer.GlobalToLocal(cloneGlobalPos);

        dragLayer.AddChild(dragPiece);
        dragPiece.SetXY(cloneLocalPos.x, cloneLocalPos.y);
        dragPiece.globalOriginalPosition = cloneLocalPos;

        // 在拖拽层中，新添加的自动就是最顶层

        // 设置克隆piece的拖拽状态
        dragPiece.isDragging = true;
        dragPiece.originalParent = this.originalParent;
        dragPiece.originalChildIndex = this.originalChildIndex;
        dragPiece.originalPosition = this.originalPosition;

        // 停止当前piece的拖拽
        this.isDragging = false;
        this.draggable = false;
        this.customDragEnabled = false;

        // 让克隆piece开始拖拽
        dragPiece.draggable = true;
        dragPiece.StartDrag(evt.touchId);
    }

    /// <summary>
    /// 设置父面板引用
    /// </summary>
    /// <param name="panel">父面板</param>
    public void SetParentPanel(JigsawPanel panel)
    {
        parentPanel = panel;
    }

    /// <summary>
    /// 获取thickness图像组件
    /// </summary>
    /// <returns>thickness图像组件</returns>
    public GLoader GetThickness()
    {
        return thickness;
    }

    /// <summary>
    /// 设置thickness的可见性
    /// </summary>
    /// <param name="visible">是否可见</param>
    public void SetThicknessVisible(bool visible)
    {
        if (thickness != null)
        {
            thickness.visible = visible;
        }
    }

    /// <summary>
    /// 设置所属的组
    /// </summary>
    /// <param name="group">拼块组</param>
    public void SetGroup(JigsawGroup group)
    {
        currentGroup = group;
    }

    /// <summary>
    /// 获取当前所属的组
    /// </summary>
    /// <returns>当前组，如果没有则返回null</returns>
    public JigsawGroup GetGroup()
    {
        return currentGroup;
    }

    /// <summary>
    /// 设置组拖拽偏移量
    /// </summary>
    /// <param name="offset">偏移量</param>
    public void SetGroupDragOffset(Vector2 offset)
    {
        groupDragOffset = offset;
    }

    /// <summary>
    /// 获取组拖拽偏移量
    /// </summary>
    /// <returns>偏移量</returns>
    public Vector2 GetGroupDragOffset()
    {
        return groupDragOffset;
    }

    /// <summary>
    /// 清除组拖拽偏移量
    /// </summary>
    public void ClearGroupDragOffset()
    {
        groupDragOffset = Vector2.zero;
    }

    /// <summary>
    /// 设置组高亮效果
    /// </summary>
    /// <param name="highlighted">是否高亮</param>
    public void SetGroupHighlight(bool highlighted)
    {
        isGroupHighlighted = highlighted;
        // TODO: 实现视觉高亮效果，例如添加边框或改变颜色
        // 这里可以通过修改拼块的颜色或添加边框来实现
        if (highlighted)
        {
            // 添加高亮效果，例如设置边框颜色
            // this.color = Color.yellow; // 示例：黄色高亮
        }
        else
        {
            // 移除高亮效果
            // this.color = Color.white; // 恢复原色
        }
    }

    /// <summary>
    /// 获取拼块在原图网格中的位置
    /// </summary>
    /// <returns>网格位置</returns>
    public Vector2Int GetOriginalGridPosition()
    {
        return JigsawGroup.GetGridPosition(pieceIndex);
    }

    /// <summary>
    /// 检查与另一个拼块是否在原图中相邻
    /// </summary>
    /// <param name="otherPiece">另一个拼块</param>
    /// <returns>是否相邻</returns>
    public bool IsAdjacentTo(JigsawPiece otherPiece)
    {
        if (otherPiece == null) return false;

        Vector2Int thisPos = GetOriginalGridPosition();
        Vector2Int otherPos = otherPiece.GetOriginalGridPosition();

        return JigsawGroup.IsAdjacent(thisPos, otherPos);
    }

    /// <summary>
    /// 检查拼块是否在操作层中
    /// </summary>
    /// <returns>是否在操作层中</returns>
    private bool IsInOperationLayer()
    {
        if (parentPanel == null) return false;

        // 检查拼块的中心点是否在操作层范围内
        Vector2 centerOffset = new Vector2(width * 0.5f, height * 0.5f);
        Vector2 globalCenterPos = LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);

        return parentPanel.IsPositionInOperationLayer(operationLayerLocalPos);
    }

    /// <summary>
    /// 触摸开始事件处理
    /// </summary>
    private void OnTouchBegin(EventContext context)
    {
        if (isDragging) return;  // 如果已经在拖拽中，不处理新的触摸

        var evt = context.inputEvent;
        touchStartPos = evt.position;
        isDragTesting = true;
        context.CaptureTouch();
    }

    /// <summary>
    /// 触摸移动事件处理
    /// </summary>
    private void OnTouchMove(EventContext context)
    {
        if (!isDragTesting) return;

        var evt = context.inputEvent;
        Vector2 currentPos = evt.position;
        Vector2 deltaPos = currentPos - touchStartPos;

        // 检查是否在listStorage中
        bool isInStorage = IsInStorage();

        if (isInStorage)
        {
            // 在listStorage中，只有垂直方向超过阈值才开始拖拽
            if (Mathf.Abs(deltaPos.y) >= VERTICAL_DRAG_THRESHOLD)
            {
                isDragTesting = false;
                customDragEnabled = true;

                // 如果是从listStorage拖拽且不是克隆piece，需要创建克隆piece
                if (!isClone)
                {
                    CreateCloneAndStartDrag(evt);
                }
                else
                {
                    // 如果已经是克隆piece，直接启动拖拽
                    draggable = true;
                    StartDrag(evt.touchId);
                }
            }
        }
        else
        {
            // 不在listStorage中，使用默认的拖拽敏感度
            int sensitivity = Stage.touchScreen ? UIConfig.touchDragSensitivity : UIConfig.clickDragSensitivity;
            if (Mathf.Abs(deltaPos.x) >= sensitivity || Mathf.Abs(deltaPos.y) >= sensitivity)
            {
                isDragTesting = false;
                customDragEnabled = true;
                draggable = true; // 启用拖拽
                StartDrag(evt.touchId);
            }
        }
    }

    /// <summary>
    /// 触摸结束事件处理
    /// </summary>
    private void OnTouchEnd(EventContext context)
    {
        isDragTesting = false;
        if (customDragEnabled)
        {
            customDragEnabled = false;
            draggable = false; // 禁用拖拽
        }
    }

    /// <summary>
    /// 检查是否在listStorage中
    /// </summary>
    private bool IsInStorage()
    {
        if (parentPanel == null) return false;

        // 检查当前父容器是否是listStorage
        GComponent currentParent = parent?.asCom;
        if (currentParent != null)
        {
            if (currentParent.name == "listStorage")
                return true;
        }
        return false;
    }

    /// <summary>
    /// 拖拽开始事件处理
    /// </summary>
    private void OnDragStart(EventContext context)
    {
        isDragging = true;
        originalParent = parent.asCom;
        originalChildIndex = originalParent.GetChildIndex(this);  // 保存原始索引位置
        originalPosition = xy;

        if (parentPanel != null)
        {
            // 注意：从listStorage的拖拽现在通过OnTouchMove中的CreateCloneAndStartDrag处理
            // 这里只处理非storage的拖拽或者已经是克隆piece的拖拽

            // 将当前位置转换为全局坐标
            Vector2 globalPos = LocalToGlobal(Vector2.zero);

            // 如果拼块在操作层中，从layerThickness中移除thickness并显示拼块自身的thickness
            if (IsInOperationLayer())
            {
                parentPanel.GetOperationLayer().RemovePieceThicknessFromLayer(this);
            }

            // 从原始父容器中移除
            // originalParent.RemoveChild(this, false);

            // 添加到JagsawPanel的拖拽层中
            var dragLayer = parentPanel.GetDragLayer();
            this.RemoveFromParent();
            dragLayer.AddChildAt(this,dragLayer.numChildren);

            // 将全局坐标转换为新父容器的本地坐标
            Vector2 newLocalPos = dragLayer.GlobalToLocal(globalPos);
            SetXY(newLocalPos.x, newLocalPos.y);
            globalOriginalPosition = newLocalPos;

            // 如果拼块属于某个组，开始组拖拽
            if (currentGroup != null)
            {
                currentGroup.StartGroupDrag(this, xy);
            }

            // 在拖拽层中，新添加的自动就是最顶层
        }
    }

    /// <summary>
    /// 拖拽移动事件处理
    /// </summary>
    private void OnDragMove(EventContext context)
    {
        if (!isDragging) return;

        // 如果拼块属于某个组，更新组拖拽
        if (currentGroup != null)
        {
            currentGroup.UpdateGroupDrag(this, xy);
        }

        // 在拖拽过程中可以添加一些视觉反馈
        // 比如高亮显示可能的放置位置等
    }

    /// <summary>
    /// 拖拽结束事件处理
    /// </summary>
    private void OnDragEnd(EventContext context)
    {
        if (!isDragging) return;

        isDragging = false;

        // 如果拼块属于某个组，结束组拖拽
        if (currentGroup != null)
        {
            currentGroup.EndGroupDrag();
        }

        // 重置自定义拖拽状态
        if (customDragEnabled)
        {
            customDragEnabled = false;
            draggable = false; // 禁用拖拽
        }

        // 检查是否拖拽到操作层区域
        HandleDropToOperationLayer();
    }

    /// <summary>
    /// 处理拖拽到操作层的逻辑
    /// </summary>
    private void HandleDropToOperationLayer()
    {
        // 将拼块中心点位置转换为操作层的本地坐标
        // 由于拼块的pivot是(0.5, 0.5)，需要获取中心点位置
        Vector2 centerOffset = new Vector2(width * 0.5f, height * 0.5f);
        Vector2 globalCenterPos = LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);

        // 检查是否在操作层范围内
        if (parentPanel.IsPositionInOperationLayer(operationLayerLocalPos))
        {
            // 获取网格坐标
            Vector2Int gridPos = parentPanel.GetGridPosition(operationLayerLocalPos);
            Vector2 targetPos = parentPanel.GetLocalPosition(gridPos);

            // 将目标位置转换回全局坐标，再转换为当前父容器的本地坐标
            Vector2 globalTargetPos = parentPanel.OperationLayerLocalToGlobal(targetPos);
            // 由于目标位置是网格中心点，需要减去拼块中心偏移，得到拼块左上角应该放置的位置
            Vector2 localTargetPos = parent.GlobalToLocal(globalTargetPos) - centerOffset;

            // 如果拼块属于组，需要同时移动组中的其他拼块
            if (currentGroup != null)
            {
                // 计算移动偏移量
                Vector2 moveOffset = localTargetPos - xy;

                // 同时移动组中的所有拼块
                foreach (var piece in currentGroup.Pieces)
                {
                    Vector2 pieceTargetPos = piece.xy + moveOffset;
                    var tween = piece.TweenMove(pieceTargetPos, 0.3f);

                    // 在缓动过程中实时更新thickness位置
                    tween.OnUpdate(() =>
                    {
                        if (parentPanel != null)
                        {
                            parentPanel.GetOperationLayer().UpdatePieceThicknessPosition(piece);
                        }
                    });

                    tween.OnComplete(() =>
                    {
                        // 每个拼块成功放置后，将thickness添加到layerThickness中
                        parentPanel.GetOperationLayer().AddPieceThicknessToLayer(piece);
                    });
                }

                // 检测并处理成组逻辑（只需要对主拖拽拼块执行一次）
                parentPanel.CheckAndCreateGroups(this);
            }
            else
            {
                // 单个拼块的移动
                var tween = TweenMove(localTargetPos, 0.3f);

                // 在缓动过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    if (parentPanel != null)
                    {
                        parentPanel.GetOperationLayer().UpdatePieceThicknessPosition(this);
                    }
                });

                tween.OnComplete(() =>
                {
                    // 拼块成功放置到操作层，将thickness添加到layerThickness中
                    parentPanel.GetOperationLayer().AddPieceThicknessToLayer(this);

                    // 检测并处理成组逻辑
                    parentPanel.CheckAndCreateGroups(this);
                });
            }

            // 如果是克隆的piece成功放置，删除原始piece
            if (isClone && originalPiece != null)
            {
                isClone = false;
                originalPiece.Dispose();
                originalPiece = null;
            }
        }
        else
        {
            if (originalPiece != null)
            {
                originalPiece.alpha = 1.0f;
            }
            ReturnToOriginalPosition();
        }
    }

    /// <summary>
    /// 回到原始位置
    /// </summary>
    private void ReturnToOriginalPosition()
    {
        // 如果拼块属于组，需要同时移动组中的其他拼块回到原始位置
        if (currentGroup != null)
        {
            // 计算移动偏移量
            Vector2 moveOffset = globalOriginalPosition - xy;

            // 同时移动组中的所有拼块
            foreach (var piece in currentGroup.Pieces)
            {
                Vector2 pieceTargetPos = piece.xy + moveOffset;
                var tween = piece.TweenMove(pieceTargetPos, 0.3f);

                // 在缓动过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    if (parentPanel != null)
                    {
                        parentPanel.GetOperationLayer().UpdatePieceThicknessPosition(piece);
                    }
                });

                tween.OnComplete(() =>
                {
                    if (piece.isDisposed) return;
                    if (piece.isClone)
                    {
                        // 如果是克隆，直接销毁
                        piece.Dispose();
                    }
                    else
                    {
                        // 如果是原始piece，则恢复其原始状态
                        piece.RemoveFromParent();
                        piece.originalParent.AddChildAt(piece, piece.originalChildIndex);
                        piece.SetXY(piece.originalPosition.x, piece.originalPosition.y);

                        // 将其厚度重新交还给OperationLayer管理
                        parentPanel.GetOperationLayer().AddPieceThicknessToLayer(piece);
                    }
                });
            }
        }
        else
        {
            // 单个拼块的返回
            var tween = TweenMove(globalOriginalPosition, 0.3f);

            // 在缓动过程中实时更新thickness位置
            tween.OnUpdate(() =>
            {
                if (parentPanel != null)
                {
                    parentPanel.GetOperationLayer().UpdatePieceThicknessPosition(this);
                }
            });

            tween.OnComplete(() =>
            {
                if (this.isDisposed) return;
                if (isClone)
                {
                    // 如果是克隆，直接销毁
                    this.Dispose();
                }
                else
                {
                    // 如果是原始piece，则恢复其原始状态
                    this.RemoveFromParent();
                    originalParent.AddChildAt(this, originalChildIndex);
                    this.SetXY(originalPosition.x, originalPosition.y);

                    // 将其厚度重新交还给OperationLayer管理
                    parentPanel.GetOperationLayer().AddPieceThicknessToLayer(this);
                }
            });
        }
    }
}

